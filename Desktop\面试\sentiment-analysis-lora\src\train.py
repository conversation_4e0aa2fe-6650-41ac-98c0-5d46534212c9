"""
LoRA微调训练脚本
使用PEFT库对BERT进行高效微调
"""

import os
import torch
import numpy as np

# 🚀 快速模式：硬编码阿里云镜像
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['HF_HUB_ENABLE_HF_TRANSFER'] = '1'

from transformers import (
    AutoTokenizer,
    AutoModelForSequenceClassification,
    TrainingArguments,
    Trainer,
    DataCollatorWithPadding
)
from peft import LoraConfig, get_peft_model, TaskType
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
import json
from data_process import prepare_data, create_datasets


def compute_metrics(eval_pred):
    """计算评估指标"""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)
    
    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='weighted')
    accuracy = accuracy_score(labels, predictions)
    
    return {
        'accuracy': accuracy,
        'f1': f1,
        'precision': precision,
        'recall': recall
    }


def create_lora_model(model_name: str = "bert-base-chinese", num_labels: int = 3):
    """创建LoRA微调模型"""
    
    # 加载基础模型
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name,
        num_labels=num_labels,
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
    )
    
    # LoRA配置 - 2024最佳实践
    lora_config = LoraConfig(
        task_type=TaskType.SEQ_CLS,
        r=16,  # rank - 平衡性能和效率
        lora_alpha=32,  # scaling factor
        lora_dropout=0.1,
        target_modules=["query", "value", "key", "dense"],  # 覆盖所有注意力层
        bias="none",
        use_rslora=False,  # 2024新特性，可选启用
        init_lora_weights=True  # 确保正确初始化
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    
    # 打印可训练参数
    model.print_trainable_parameters()
    
    return model


def train_model(
    model_name: str = "bert-base-chinese",
    output_dir: str = "./models",
    num_epochs: int = 3,
    batch_size: int = 8,
    learning_rate: float = 2e-4,
    max_length: int = 512,
    data_source: str = "huggingface",
    dataset_name: str = "seamew/ChnSentiCorp",
    data_path: str = None
):
    """训练模型"""

    print("开始训练准备...")

    # 准备数据
    train_df, test_df = prepare_data(
        data_source=data_source,
        dataset_name=dataset_name,
        data_path=data_path
    )
    train_dataset, test_dataset, tokenizer = create_datasets(
        train_df, test_df, model_name, max_length
    )
    
    # 创建模型
    model = create_lora_model(model_name, num_labels=3)
    
    # 数据整理器
    data_collator = DataCollatorWithPadding(tokenizer=tokenizer)
    
    # 训练参数 - 2024最佳实践
    training_args = TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=num_epochs,
        per_device_train_batch_size=batch_size,
        per_device_eval_batch_size=batch_size,
        warmup_steps=min(100, len(train_dataset) // (batch_size * 10)),  # 动态warmup
        weight_decay=0.01,
        logging_dir=f"{output_dir}/logs",
        logging_steps=max(1, len(train_dataset) // (batch_size * 20)),  # 动态logging
        evaluation_strategy="epoch",
        save_strategy="epoch",
        load_best_model_at_end=True,
        metric_for_best_model="f1",
        greater_is_better=True,
        learning_rate=learning_rate,
        lr_scheduler_type="cosine",  # 余弦学习率调度
        fp16=torch.cuda.is_available(),
        bf16=torch.cuda.is_available() and torch.cuda.is_bf16_supported(),  # 优先使用bf16
        dataloader_pin_memory=False,
        remove_unused_columns=False,
        report_to=None,  # 禁用wandb等日志记录
        save_total_limit=2,  # 只保留最好的2个检查点
        seed=42,  # 确保可重现性
        data_seed=42,
        optim="adamw_torch",  # 明确指定优化器
        gradient_checkpointing=True if torch.cuda.is_available() else False,  # 节省显存
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=test_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
        compute_metrics=compute_metrics,
    )
    
    print("开始训练...")
    
    # 训练模型
    trainer.train()
    
    # 保存模型
    trainer.save_model()
    tokenizer.save_pretrained(output_dir)
    
    # 保存训练配置
    config = {
        "model_name": model_name,
        "num_epochs": num_epochs,
        "batch_size": batch_size,
        "learning_rate": learning_rate,
        "max_length": max_length,
        "data_source": data_source,
        "dataset_name": dataset_name,
        "data_path": data_path,
        "train_size": len(train_dataset),
        "test_size": len(test_dataset)
    }
    
    with open(f"{output_dir}/training_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"训练完成！模型已保存到 {output_dir}")
    
    # 最终评估
    print("进行最终评估...")
    eval_results = trainer.evaluate()
    print("评估结果:")
    for key, value in eval_results.items():
        print(f"  {key}: {value:.4f}")
    
    return trainer, eval_results


if __name__ == "__main__":
    # 检查CUDA可用性
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"使用设备: {device}")

    # 创建输出目录
    os.makedirs("./models", exist_ok=True)

    # 训练配置
    print("=== 训练配置 ===")
    print("数据源选项:")
    print("1. huggingface - 使用Hugging Face数据集 (推荐)")
    print("2. sample - 使用示例数据集 (快速测试)")
    print("3. local - 使用本地CSV文件")

    # 开始训练 - 默认使用Hugging Face数据集
    print("\n开始训练 (使用Hugging Face数据集)...")
    trainer, results = train_model(
        model_name="bert-base-chinese",
        output_dir="./models",
        num_epochs=3,
        batch_size=8,
        learning_rate=2e-4,
        data_source="huggingface",
        dataset_name="seamew/ChnSentiCorp"
    )
