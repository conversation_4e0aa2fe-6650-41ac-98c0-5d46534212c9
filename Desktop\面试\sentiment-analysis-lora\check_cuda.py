"""
检查CUDA环境和GPU状态
"""

import torch
import sys

def check_cuda_environment():
    """检查CUDA环境"""
    
    print("=== CUDA环境检查 ===")
    
    # PyTorch版本
    print(f"PyTorch版本: {torch.__version__}")
    
    # CUDA可用性
    cuda_available = torch.cuda.is_available()
    print(f"CUDA可用: {cuda_available}")
    
    if cuda_available:
        # GPU信息
        gpu_count = torch.cuda.device_count()
        print(f"GPU数量: {gpu_count}")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(f"GPU {i}: {gpu_name}")
            print(f"  显存: {gpu_memory:.1f}GB")
        
        # 当前GPU
        current_device = torch.cuda.current_device()
        print(f"当前GPU: {current_device}")
        
        # CUDA版本
        cuda_version = torch.version.cuda
        print(f"CUDA版本: {cuda_version}")
        
        # cuDNN版本
        if torch.backends.cudnn.is_available():
            cudnn_version = torch.backends.cudnn.version()
            print(f"cuDNN版本: {cudnn_version}")
        
        # 测试GPU计算
        print("\n=== GPU计算测试 ===")
        try:
            # 创建测试张量
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            
            # 矩阵乘法测试
            import time
            start_time = time.time()
            z = torch.mm(x, y)
            end_time = time.time()
            
            print(f"✅ GPU计算测试成功")
            print(f"矩阵乘法耗时: {(end_time - start_time)*1000:.2f}ms")
            
            # 显存使用情况
            memory_allocated = torch.cuda.memory_allocated() / 1e9
            memory_reserved = torch.cuda.memory_reserved() / 1e9
            print(f"已分配显存: {memory_allocated:.2f}GB")
            print(f"已保留显存: {memory_reserved:.2f}GB")
            
            return True
            
        except Exception as e:
            print(f"❌ GPU计算测试失败: {e}")
            return False
    
    else:
        print("\n❌ CUDA不可用，可能的原因:")
        print("1. 没有安装CUDA版本的PyTorch")
        print("2. CUDA驱动版本不兼容")
        print("3. GPU不支持CUDA")
        
        print("\n解决方案:")
        print("1. 重新安装CUDA版本的PyTorch:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        print("2. 检查NVIDIA驱动是否最新")
        
        return False

def get_optimal_batch_size():
    """根据GPU显存推荐批次大小"""
    
    if not torch.cuda.is_available():
        return 8
    
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
    
    if gpu_memory >= 12:  # RTX 3060 12GB
        return 32
    elif gpu_memory >= 8:  # RTX 3060 8GB
        return 24
    elif gpu_memory >= 6:
        return 16
    else:
        return 8

if __name__ == "__main__":
    success = check_cuda_environment()
    
    if success:
        batch_size = get_optimal_batch_size()
        print(f"\n🎯 推荐配置:")
        print(f"批次大小: {batch_size}")
        print(f"预计训练时间: 10-20分钟 (vs CPU的30小时)")
        print(f"性能提升: 100-200倍")
        
        print(f"\n🚀 准备好GPU训练了！")
    else:
        print(f"\n⚠️ 需要先解决CUDA环境问题")
