"""
Gradio Web应用
提供友好的Web界面进行情感分析
"""

import gradio as gr
import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from io import BytesIO
import base64

# 添加src目录到路径
sys.path.append('./src')

try:
    from inference import SentimentPredictor
except ImportError:
    print("无法导入推理模块，请确保src目录存在且包含inference.py")
    sys.exit(1)


class SentimentAnalysisApp:
    """情感分析Web应用"""
    
    def __init__(self, model_path: str = "./models"):
        self.model_path = model_path
        self.predictor = None
        self.load_model()
    
    def load_model(self):
        """加载模型"""
        if os.path.exists(self.model_path):
            try:
                self.predictor = SentimentPredictor(self.model_path)
                return "✅ 模型加载成功！"
            except Exception as e:
                return f"❌ 模型加载失败: {str(e)}"
        else:
            return f"❌ 模型路径不存在: {self.model_path}"
    
    def predict_single(self, text):
        """单文本预测"""
        if not self.predictor:
            return "❌ 模型未加载", None, None
        
        if not text.strip():
            return "⚠️ 请输入有效文本", None, None
        
        try:
            result = self.predictor.predict(text.strip())
            
            # 格式化结果
            prediction_text = f"""
            📝 **输入文本**: {result['text']}
            
            🎯 **预测结果**: {result['predicted_class']}
            
            📊 **置信度**: {result['confidence']:.4f}
            """
            
            # 创建概率分布图
            prob_data = result['all_probabilities']
            fig = self.create_probability_chart(prob_data)
            
            # 详细概率信息
            prob_text = "**各类别概率**:\n"
            for class_name, prob in prob_data.items():
                prob_text += f"- {class_name}: {prob:.4f}\n"
            
            return prediction_text, fig, prob_text
            
        except Exception as e:
            return f"❌ 预测失败: {str(e)}", None, None
    
    def predict_batch(self, file):
        """批量预测"""
        if not self.predictor:
            return "❌ 模型未加载", None
        
        if file is None:
            return "⚠️ 请上传CSV文件", None
        
        try:
            # 读取CSV文件
            df = pd.read_csv(file.name)
            
            if 'text' not in df.columns:
                return "❌ CSV文件必须包含'text'列", None
            
            texts = df['text'].astype(str).tolist()
            
            # 批量预测
            results = self.predictor.predict_batch(texts)
            
            # 创建结果DataFrame
            result_df = pd.DataFrame([
                {
                    'text': r['text'],
                    'predicted_class': r['predicted_class'],
                    'confidence': r['confidence'],
                    'negative_prob': r['all_probabilities']['负面'],
                    'positive_prob': r['all_probabilities']['正面'],
                    'neutral_prob': r['all_probabilities']['中性']
                }
                for r in results
            ])
            
            # 保存结果
            output_path = "batch_prediction_results.csv"
            result_df.to_csv(output_path, index=False, encoding='utf-8-sig')
            
            # 创建统计图表
            stats_fig = self.create_batch_stats_chart(result_df)
            
            summary = f"""
            ✅ **批量预测完成**
            
            📊 **统计信息**:
            - 总文本数: {len(result_df)}
            - 正面情感: {len(result_df[result_df['predicted_class'] == '正面'])}
            - 负面情感: {len(result_df[result_df['predicted_class'] == '负面'])}
            - 中性情感: {len(result_df[result_df['predicted_class'] == '中性'])}
            - 平均置信度: {result_df['confidence'].mean():.4f}
            
            💾 结果已保存到: {output_path}
            """
            
            return summary, stats_fig
            
        except Exception as e:
            return f"❌ 批量预测失败: {str(e)}", None
    
    def create_probability_chart(self, prob_data):
        """创建概率分布图"""
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, ax = plt.subplots(figsize=(8, 5))
        
        classes = list(prob_data.keys())
        probs = list(prob_data.values())
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']
        
        bars = ax.bar(classes, probs, color=colors, alpha=0.8)
        
        # 添加数值标签
        for bar, prob in zip(bars, probs):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{prob:.3f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_title('情感分类概率分布', fontsize=14, fontweight='bold')
        ax.set_ylabel('概率', fontsize=12)
        ax.set_ylim(0, 1)
        ax.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def create_batch_stats_chart(self, df):
        """创建批量预测统计图表"""
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 情感分布饼图
        sentiment_counts = df['predicted_class'].value_counts()
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']
        ax1.pie(sentiment_counts.values, labels=sentiment_counts.index, 
                autopct='%1.1f%%', colors=colors, startangle=90)
        ax1.set_title('情感分布', fontsize=14, fontweight='bold')
        
        # 置信度分布直方图
        ax2.hist(df['confidence'], bins=20, color='skyblue', alpha=0.7, edgecolor='black')
        ax2.set_title('置信度分布', fontsize=14, fontweight='bold')
        ax2.set_xlabel('置信度', fontsize=12)
        ax2.set_ylabel('频次', fontsize=12)
        ax2.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        return fig


def create_interface():
    """创建Gradio界面"""
    
    app = SentimentAnalysisApp()
    
    # 检查模型状态
    model_status = app.load_model()
    
    with gr.Blocks(title="中文情感分析系统", theme=gr.themes.Soft()) as interface:
        
        gr.Markdown("""
        # 🎭 中文情感分析系统
        
        基于LoRA微调的BERT模型，支持中文文本情感分析
        
        **支持的情感类别**: 正面 😊 | 负面 😞 | 中性 😐
        """)
        
        # 模型状态显示
        gr.Markdown(f"**模型状态**: {model_status}")
        
        with gr.Tabs():
            
            # 单文本预测标签页
            with gr.TabItem("🔍 单文本分析"):
                with gr.Row():
                    with gr.Column(scale=2):
                        text_input = gr.Textbox(
                            label="输入文本",
                            placeholder="请输入要分析的中文文本...",
                            lines=3
                        )
                        predict_btn = gr.Button("🚀 开始分析", variant="primary")
                    
                    with gr.Column(scale=3):
                        prediction_output = gr.Markdown(label="预测结果")
                        prob_details = gr.Markdown(label="详细概率")
                
                probability_chart = gr.Plot(label="概率分布图")
                
                # 示例文本
                gr.Examples(
                    examples=[
                        ["这个产品质量很好，非常满意！"],
                        ["服务态度差，不推荐购买"],
                        ["价格合理，性价比不错"],
                        ["物流太慢了，很失望"],
                        ["整体还可以，符合预期"]
                    ],
                    inputs=text_input
                )
            
            # 批量预测标签页
            with gr.TabItem("📊 批量分析"):
                with gr.Row():
                    with gr.Column():
                        file_input = gr.File(
                            label="上传CSV文件",
                            file_types=[".csv"],
                            type="file"
                        )
                        gr.Markdown("""
                        **文件格式要求**:
                        - CSV格式文件
                        - 必须包含'text'列
                        - 支持UTF-8编码
                        """)
                        batch_predict_btn = gr.Button("📈 批量分析", variant="primary")
                    
                    with gr.Column():
                        batch_output = gr.Markdown(label="批量分析结果")
                
                batch_chart = gr.Plot(label="统计图表")
        
        # 事件绑定
        predict_btn.click(
            fn=app.predict_single,
            inputs=text_input,
            outputs=[prediction_output, probability_chart, prob_details]
        )
        
        batch_predict_btn.click(
            fn=app.predict_batch,
            inputs=file_input,
            outputs=[batch_output, batch_chart]
        )
        
        # 页脚信息
        gr.Markdown("""
        ---
        💡 **技术栈**: BERT + LoRA + Hugging Face Transformers + Gradio
        
        🔧 **开发者**: 基于LoRA高效微调技术实现
        """)
    
    return interface


if __name__ == "__main__":
    # 创建并启动界面
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
