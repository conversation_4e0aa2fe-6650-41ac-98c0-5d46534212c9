"""
模型评估脚本
对训练好的LoRA模型进行详细评估
"""

import os
# 🚀 快速模式：硬编码阿里云镜像
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['HF_HUB_ENABLE_HF_TRANSFER'] = '1'

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score,
    precision_recall_fscore_support,
    confusion_matrix,
    classification_report
)
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from peft import PeftModel
import json
from data_process import prepare_data, create_datasets


def load_model(model_path: str, base_model_name: str = "bert-base-chinese"):
    """加载训练好的LoRA模型"""
    
    # 加载tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    # 加载基础模型
    base_model = AutoModelForSequenceClassification.from_pretrained(
        base_model_name,
        num_labels=3,
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
    )
    
    # 加载LoRA权重
    model = PeftModel.from_pretrained(base_model, model_path)
    
    return model, tokenizer


def predict_batch(model, tokenizer, texts, max_length=512, batch_size=8):
    """批量预测"""
    model.eval()
    device = next(model.parameters()).device
    
    predictions = []
    probabilities = []
    
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i+batch_size]
        
        # 编码
        inputs = tokenizer(
            batch_texts,
            truncation=True,
            padding=True,
            max_length=max_length,
            return_tensors='pt'
        ).to(device)
        
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            probs = torch.softmax(logits, dim=-1)
            preds = torch.argmax(logits, dim=-1)
            
            predictions.extend(preds.cpu().numpy())
            probabilities.extend(probs.cpu().numpy())
    
    return np.array(predictions), np.array(probabilities)


def evaluate_model(model_path: str, base_model_name: str = "bert-base-chinese"):
    """全面评估模型"""
    
    print("加载模型...")
    model, tokenizer = load_model(model_path, base_model_name)
    
    # 移动到GPU（如果可用）
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = model.to(device)
    
    print("准备测试数据...")
    train_df, test_df = prepare_data()
    
    # 获取测试数据
    test_texts = test_df['text'].tolist()
    test_labels = test_df['label'].tolist()
    
    print("开始预测...")
    predictions, probabilities = predict_batch(model, tokenizer, test_texts)
    
    # 计算指标
    accuracy = accuracy_score(test_labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(
        test_labels, predictions, average='weighted'
    )
    
    # 分类报告
    class_names = ['负面', '正面', '中性']
    report = classification_report(
        test_labels, predictions, 
        target_names=class_names,
        output_dict=True
    )
    
    # 混淆矩阵
    cm = confusion_matrix(test_labels, predictions)
    
    # 打印结果
    print("\n=== 模型评估结果 ===")
    print(f"准确率: {accuracy:.4f}")
    print(f"精确率: {precision:.4f}")
    print(f"召回率: {recall:.4f}")
    print(f"F1分数: {f1:.4f}")
    
    print("\n=== 详细分类报告 ===")
    for class_name, metrics in report.items():
        if class_name in class_names:
            print(f"{class_name}:")
            print(f"  精确率: {metrics['precision']:.4f}")
            print(f"  召回率: {metrics['recall']:.4f}")
            print(f"  F1分数: {metrics['f1-score']:.4f}")
            print(f"  支持数: {metrics['support']}")
    
    # 可视化结果
    plot_results(cm, class_names, accuracy, f1)
    
    # 保存评估结果
    results = {
        "accuracy": float(accuracy),
        "precision": float(precision),
        "recall": float(recall),
        "f1": float(f1),
        "classification_report": report,
        "confusion_matrix": cm.tolist()
    }
    
    results_path = os.path.join(model_path, "evaluation_results.json")
    with open(results_path, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n评估结果已保存到: {results_path}")
    
    return results


def plot_results(cm, class_names, accuracy, f1):
    """绘制评估结果图表"""
    
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # 混淆矩阵热力图
    sns.heatmap(
        cm, 
        annot=True, 
        fmt='d', 
        cmap='Blues',
        xticklabels=class_names,
        yticklabels=class_names,
        ax=axes[0]
    )
    axes[0].set_title('混淆矩阵')
    axes[0].set_xlabel('预测标签')
    axes[0].set_ylabel('真实标签')
    
    # 性能指标柱状图
    metrics = ['准确率', 'F1分数']
    values = [accuracy, f1]
    
    bars = axes[1].bar(metrics, values, color=['skyblue', 'lightcoral'])
    axes[1].set_title('模型性能指标')
    axes[1].set_ylabel('分数')
    axes[1].set_ylim(0, 1)
    
    # 在柱状图上添加数值标签
    for bar, value in zip(bars, values):
        axes[1].text(
            bar.get_x() + bar.get_width()/2, 
            bar.get_height() + 0.01,
            f'{value:.3f}', 
            ha='center', 
            va='bottom'
        )
    
    plt.tight_layout()
    plt.savefig('./models/evaluation_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("评估图表已保存到: ./models/evaluation_results.png")


def test_single_prediction(model_path: str, text: str):
    """测试单个文本预测"""
    
    model, tokenizer = load_model(model_path)
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = model.to(device)
    
    predictions, probabilities = predict_batch(model, tokenizer, [text])
    
    class_names = ['负面', '正面', '中性']
    pred_class = class_names[predictions[0]]
    confidence = probabilities[0][predictions[0]]
    
    print(f"输入文本: {text}")
    print(f"预测结果: {pred_class}")
    print(f"置信度: {confidence:.4f}")
    
    print("各类别概率:")
    for i, (class_name, prob) in enumerate(zip(class_names, probabilities[0])):
        print(f"  {class_name}: {prob:.4f}")


if __name__ == "__main__":
    model_path = "./models"
    
    if os.path.exists(model_path):
        print("开始模型评估...")
        results = evaluate_model(model_path)
        
        # 测试单个预测
        print("\n=== 单个预测测试 ===")
        test_texts = [
            "这个产品真的很棒，强烈推荐！",
            "质量太差了，完全不值这个价格",
            "还可以吧，没什么特别的"
        ]
        
        for text in test_texts:
            test_single_prediction(model_path, text)
            print()
    else:
        print(f"模型路径不存在: {model_path}")
        print("请先运行训练脚本生成模型")
