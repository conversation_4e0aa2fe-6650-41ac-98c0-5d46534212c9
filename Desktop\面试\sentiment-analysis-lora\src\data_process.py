"""
数据预处理模块
处理中文情感分析数据集，包括数据清洗、分词、编码等
"""

import pandas as pd
import jieba
import re
from typing import List, Tuple, Dict
from sklearn.model_selection import train_test_split
from transformers import AutoTokenizer
import torch
from torch.utils.data import Dataset


class SentimentDataset(Dataset):
    """情感分析数据集类"""
    
    def __init__(self, texts: List[str], labels: List[int], tokenizer, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        # 文本编码
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }


def clean_text(text: str) -> str:
    """清洗文本数据"""
    if pd.isna(text):
        return ""
    
    # 移除特殊字符，保留中文、英文、数字
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', str(text))
    # 移除多余空格
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text


def load_sample_data() -> pd.DataFrame:
    """创建示例数据集（实际项目中应该加载真实数据）"""
    sample_data = {
        'text': [
            '这个产品质量很好，非常满意！',
            '服务态度差，不推荐购买',
            '价格合理，性价比不错',
            '物流太慢了，很失望',
            '包装精美，产品质量优秀',
            '客服回复及时，解决问题很快',
            '产品有瑕疵，需要改进',
            '整体还可以，符合预期',
            '超出预期，强烈推荐！',
            '一般般，没什么特别的'
        ],
        'label': [1, 0, 1, 0, 1, 1, 0, 2, 1, 2]  # 0:负面, 1:正面, 2:中性
    }
    return pd.DataFrame(sample_data)


def prepare_data(data_path: str = None, test_size: float = 0.2, random_state: int = 42) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """准备训练和测试数据"""
    
    if data_path:
        # 加载真实数据
        df = pd.read_csv(data_path)
    else:
        # 使用示例数据
        df = load_sample_data()
        print("使用示例数据集进行演示")
    
    # 数据清洗
    df['text'] = df['text'].apply(clean_text)
    
    # 移除空文本
    df = df[df['text'].str.len() > 0]
    
    # 分割数据集
    train_df, test_df = train_test_split(
        df, 
        test_size=test_size, 
        random_state=random_state,
        stratify=df['label']
    )
    
    return train_df, test_df


def create_datasets(train_df: pd.DataFrame, test_df: pd.DataFrame, 
                   model_name: str = "bert-base-chinese", max_length: int = 512):
    """创建PyTorch数据集"""
    
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    train_dataset = SentimentDataset(
        texts=train_df['text'].tolist(),
        labels=train_df['label'].tolist(),
        tokenizer=tokenizer,
        max_length=max_length
    )
    
    test_dataset = SentimentDataset(
        texts=test_df['text'].tolist(),
        labels=test_df['label'].tolist(),
        tokenizer=tokenizer,
        max_length=max_length
    )
    
    return train_dataset, test_dataset, tokenizer


if __name__ == "__main__":
    # 测试数据处理流程
    print("开始数据预处理...")
    
    train_df, test_df = prepare_data()
    print(f"训练集大小: {len(train_df)}")
    print(f"测试集大小: {len(test_df)}")
    
    train_dataset, test_dataset, tokenizer = create_datasets(train_df, test_df)
    print(f"数据集创建完成")
    print(f"词汇表大小: {tokenizer.vocab_size}")
