"""
数据预处理模块
处理中文情感分析数据集，包括数据清洗、分词、编码等
支持从Hugging Face加载数据集和本地数据
"""

import pandas as pd
import jieba
import re
from typing import List, Tuple, Dict, Optional
from sklearn.model_selection import train_test_split
from transformers import AutoTokenizer
from datasets import load_dataset, Dataset as HFDataset
import torch
from torch.utils.data import Dataset


class SentimentDataset(Dataset):
    """情感分析数据集类"""
    
    def __init__(self, texts: List[str], labels: List[int], tokenizer, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        # 文本编码
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }


def clean_text(text: str) -> str:
    """清洗文本数据"""
    if pd.isna(text):
        return ""
    
    # 移除特殊字符，保留中文、英文、数字
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', str(text))
    # 移除多余空格
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text


def load_huggingface_dataset(dataset_name: str = "seamew/ChnSentiCorp") -> Tuple[pd.DataFrame, pd.DataFrame]:
    """从Hugging Face加载中文情感分析数据集"""
    try:
        print(f"正在从Hugging Face加载数据集: {dataset_name}")

        # 尝试不同的数据集加载方式
        if dataset_name == "seamew/ChnSentiCorp":
            # 对于ChnSentiCorp，尝试使用trust_remote_code
            try:
                dataset = load_dataset(dataset_name, trust_remote_code=True)
            except:
                # 如果失败，尝试其他中文情感数据集
                print("ChnSentiCorp加载失败，尝试使用其他数据集...")
                dataset_name = "emotion"
                dataset = load_dataset(dataset_name)
        else:
            dataset = load_dataset(dataset_name)

        # 转换为pandas DataFrame
        if 'train' in dataset and 'validation' in dataset:
            train_df = pd.DataFrame(dataset['train'])
            test_df = pd.DataFrame(dataset['validation'])
        elif 'train' in dataset and 'test' in dataset:
            train_df = pd.DataFrame(dataset['train'])
            test_df = pd.DataFrame(dataset['test'])
        else:
            # 如果只有训练集，手动分割
            full_df = pd.DataFrame(dataset['train'])
            train_df, test_df = train_test_split(full_df, test_size=0.2, random_state=42)

        # 标准化列名
        if 'text' not in train_df.columns:
            # 尝试常见的文本列名
            text_cols = ['sentence', 'content', 'review', 'comment']
            for col in text_cols:
                if col in train_df.columns:
                    train_df = train_df.rename(columns={col: 'text'})
                    test_df = test_df.rename(columns={col: 'text'})
                    break

        if 'label' not in train_df.columns:
            # 尝试常见的标签列名
            label_cols = ['labels', 'sentiment', 'emotion']
            for col in label_cols:
                if col in train_df.columns:
                    train_df = train_df.rename(columns={col: 'label'})
                    test_df = test_df.rename(columns={col: 'label'})
                    break

        # 如果是emotion数据集，只取前3个类别并重新映射
        if dataset_name == "emotion":
            print("使用emotion数据集，映射为三分类...")
            # emotion数据集: 0=sadness, 1=joy, 2=love, 3=anger, 4=fear, 5=surprise
            # 映射为: 0=负面(sadness,anger,fear), 1=正面(joy,love), 2=中性(surprise)
            label_mapping = {0: 0, 1: 1, 2: 1, 3: 0, 4: 0, 5: 2}
            train_df['label'] = train_df['label'].map(label_mapping)
            test_df['label'] = test_df['label'].map(label_mapping)

        print(f"数据集加载成功！")
        print(f"训练集大小: {len(train_df)}")
        print(f"测试集大小: {len(test_df)}")
        print(f"标签分布: {train_df['label'].value_counts().to_dict()}")

        return train_df, test_df

    except Exception as e:
        print(f"从Hugging Face加载数据集失败: {e}")
        print("将使用本地示例数据集")
        return None, None


def load_sample_data() -> pd.DataFrame:
    """创建示例数据集（备用方案）"""
    sample_data = {
        'text': [
            # 正面评论 (标签1)
            '这个产品质量很好，非常满意！',
            '价格合理，性价比不错',
            '包装精美，产品质量优秀',
            '客服回复及时，解决问题很快',
            '超出预期，强烈推荐！',
            '发货速度很快，包装也很好',
            '非常棒的购物体验，会再次购买',
            '性价比很高，推荐给朋友',
            '产品功能强大，使用方便',
            '完全超出了我的预期',
            # 负面评论 (标签0)
            '服务态度差，不推荐购买',
            '物流太慢了，很失望',
            '产品有瑕疵，需要改进',
            '质量太差了，完全不值这个价格',
            '产品描述与实际不符',
            '包装简陋，产品一般',
            '非常失望，不会再买了',
            '质量一般，价格偏高',
            '有点小问题，但总体满意',
            '客服态度不好，解决问题慢',
            # 中性评论 (标签2)
            '整体还可以，符合预期',
            '一般般，没什么特别的',
            '还行吧，凑合能用',
            '有些功能还需要完善',
            '整体感觉还可以',
            '物流很快，包装完好',
            '产品设计很人性化',
            '使用体验不错，值得购买',
            '服务很周到，产品也不错',
            '价格合适，质量还行'
        ],
        'label': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  # 10个正面
                 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,  # 10个负面
                 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]  # 10个中性
    }
    return pd.DataFrame(sample_data)


def load_local_csv_data(file_path: str) -> pd.DataFrame:
    """加载本地CSV数据"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        print(f"成功加载本地数据: {file_path}")
        print(f"数据大小: {len(df)}")
        return df
    except Exception as e:
        print(f"加载本地数据失败: {e}")
        return None


def prepare_data(
    data_source: str = "huggingface",
    dataset_name: str = "seamew/ChnSentiCorp",
    data_path: str = None,
    test_size: float = 0.2,
    random_state: int = 42
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    准备训练和测试数据

    Args:
        data_source: 数据源类型 ("huggingface", "local", "sample")
        dataset_name: Hugging Face数据集名称
        data_path: 本地数据文件路径
        test_size: 测试集比例
        random_state: 随机种子
    """

    train_df, test_df = None, None

    if data_source == "huggingface":
        # 从Hugging Face加载数据
        train_df, test_df = load_huggingface_dataset(dataset_name)

    elif data_source == "local" and data_path:
        # 加载本地数据
        df = load_local_csv_data(data_path)
        if df is not None:
            train_df, test_df = train_test_split(
                df, test_size=test_size, random_state=random_state,
                stratify=df['label'] if 'label' in df.columns else None
            )

    # 如果上述方法都失败，使用示例数据
    if train_df is None or test_df is None:
        print("使用示例数据集进行演示")
        df = load_sample_data()
        train_df, test_df = train_test_split(
            df, test_size=test_size, random_state=random_state,
            stratify=df['label']
        )

    # 数据清洗
    train_df['text'] = train_df['text'].apply(clean_text)
    test_df['text'] = test_df['text'].apply(clean_text)

    # 移除空文本
    train_df = train_df[train_df['text'].str.len() > 0]
    test_df = test_df[test_df['text'].str.len() > 0]

    # 重置索引
    train_df = train_df.reset_index(drop=True)
    test_df = test_df.reset_index(drop=True)

    print(f"数据准备完成:")
    print(f"  训练集: {len(train_df)} 条")
    print(f"  测试集: {len(test_df)} 条")
    print(f"  训练集标签分布: {train_df['label'].value_counts().to_dict()}")

    return train_df, test_df


def create_datasets(train_df: pd.DataFrame, test_df: pd.DataFrame, 
                   model_name: str = "bert-base-chinese", max_length: int = 512):
    """创建PyTorch数据集"""
    
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    train_dataset = SentimentDataset(
        texts=train_df['text'].tolist(),
        labels=train_df['label'].tolist(),
        tokenizer=tokenizer,
        max_length=max_length
    )
    
    test_dataset = SentimentDataset(
        texts=test_df['text'].tolist(),
        labels=test_df['label'].tolist(),
        tokenizer=tokenizer,
        max_length=max_length
    )
    
    return train_dataset, test_dataset, tokenizer


def get_available_datasets():
    """获取可用的数据集列表"""
    datasets_info = {
        "seamew/ChnSentiCorp": {
            "description": "中文情感语料库，包含酒店、笔记本电脑、书籍等评论",
            "size": "约12K条数据",
            "labels": "二分类 (正面/负面)"
        },
        "emotion": {
            "description": "多语言情感数据集，包含6种基本情感",
            "size": "约16K条英文数据",
            "labels": "6分类 (sadness, joy, love, anger, fear, surprise)"
        },
        "local": {
            "description": "本地CSV文件，需要包含text和label列",
            "size": "用户自定义",
            "labels": "用户自定义"
        },
        "sample": {
            "description": "内置示例数据集，用于快速测试",
            "size": "10条数据",
            "labels": "三分类 (正面/负面/中性)"
        }
    }
    return datasets_info


if __name__ == "__main__":
    # 显示可用数据集
    print("=== 可用数据集 ===")
    datasets_info = get_available_datasets()
    for name, info in datasets_info.items():
        print(f"\n📊 {name}")
        print(f"   描述: {info['description']}")
        print(f"   大小: {info['size']}")
        print(f"   标签: {info['labels']}")

    print("\n" + "="*50)
    print("开始数据预处理测试...")

    # 测试不同数据源
    test_sources = [
        ("huggingface", "seamew/ChnSentiCorp"),
        ("sample", None)
    ]

    for source, dataset_name in test_sources:
        print(f"\n🔄 测试数据源: {source}")
        if dataset_name:
            print(f"   数据集: {dataset_name}")

        try:
            if source == "huggingface":
                train_df, test_df = prepare_data(
                    data_source="huggingface",
                    dataset_name=dataset_name
                )
            else:
                train_df, test_df = prepare_data(data_source="sample")

            # 创建数据集
            train_dataset, test_dataset, tokenizer = create_datasets(train_df, test_df)
            print(f"✅ 数据集创建成功")
            print(f"   词汇表大小: {tokenizer.vocab_size}")
            print(f"   训练样本: {len(train_dataset)}")
            print(f"   测试样本: {len(test_dataset)}")

            # 显示样本
            print(f"   样本预览:")
            for i in range(min(3, len(train_df))):
                text = train_df.iloc[i]['text'][:50] + "..." if len(train_df.iloc[i]['text']) > 50 else train_df.iloc[i]['text']
                label = train_df.iloc[i]['label']
                print(f"     文本: {text}")
                print(f"     标签: {label}")

            break  # 成功后退出循环

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            continue

    print("\n🎉 数据预处理测试完成！")
