"""
离线演示版本
不依赖大模型下载，使用简单分类器演示完整流程
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report
import jieba
import pickle
import os

class SimpleChineseSentimentClassifier:
    """简单的中文情感分类器（不依赖大模型）"""
    
    def __init__(self):
        self.vectorizer = TfidfVectorizer(max_features=1000)
        self.classifier = LogisticRegression(random_state=42)
        self.label_names = {0: '负面', 1: '正面', 2: '中性'}
    
    def preprocess_text(self, text):
        """文本预处理"""
        # 分词
        words = jieba.cut(text)
        return ' '.join(words)
    
    def train(self, texts, labels):
        """训练模型"""
        print("开始训练简单分类器...")
        
        # 预处理文本
        processed_texts = [self.preprocess_text(text) for text in texts]
        
        # 特征提取
        X = self.vectorizer.fit_transform(processed_texts)
        
        # 训练分类器
        self.classifier.fit(X, labels)
        
        print("✅ 训练完成")
    
    def predict(self, texts):
        """预测"""
        if isinstance(texts, str):
            texts = [texts]
        
        # 预处理
        processed_texts = [self.preprocess_text(text) for text in texts]
        
        # 特征提取
        X = self.vectorizer.transform(processed_texts)
        
        # 预测
        predictions = self.classifier.predict(X)
        probabilities = self.classifier.predict_proba(X)
        
        results = []
        for i, text in enumerate(texts):
            result = {
                'text': text,
                'predicted_class': self.label_names[predictions[i]],
                'confidence': probabilities[i][predictions[i]],
                'all_probabilities': {
                    self.label_names[j]: prob 
                    for j, prob in enumerate(probabilities[i])
                }
            }
            results.append(result)
        
        return results if len(results) > 1 else results[0]
    
    def evaluate(self, texts, labels):
        """评估模型"""
        processed_texts = [self.preprocess_text(text) for text in texts]
        X = self.vectorizer.transform(processed_texts)
        predictions = self.classifier.predict(X)
        
        accuracy = accuracy_score(labels, predictions)
        report = classification_report(labels, predictions, target_names=list(self.label_names.values()))
        
        return accuracy, report
    
    def save(self, path):
        """保存模型"""
        model_data = {
            'vectorizer': self.vectorizer,
            'classifier': self.classifier,
            'label_names': self.label_names
        }
        with open(path, 'wb') as f:
            pickle.dump(model_data, f)
        print(f"模型已保存到: {path}")
    
    def load(self, path):
        """加载模型"""
        with open(path, 'rb') as f:
            model_data = pickle.load(f)
        
        self.vectorizer = model_data['vectorizer']
        self.classifier = model_data['classifier']
        self.label_names = model_data['label_names']
        print(f"模型已从 {path} 加载")

def create_demo_data():
    """创建演示数据"""
    data = {
        'text': [
            # 正面评论
            '这个产品质量很好，非常满意！',
            '价格合理，性价比不错',
            '包装精美，产品质量优秀',
            '客服回复及时，解决问题很快',
            '超出预期，强烈推荐！',
            '发货速度很快，包装也很好',
            '非常棒的购物体验，会再次购买',
            '性价比很高，推荐给朋友',
            '产品功能强大，使用方便',
            '完全超出了我的预期',
            '服务态度很好，产品也不错',
            '物流很快，包装完好',
            '产品设计很人性化',
            '使用体验不错，值得购买',
            '质量很好，价格实惠',
            
            # 负面评论
            '服务态度差，不推荐购买',
            '物流太慢了，很失望',
            '产品有瑕疵，需要改进',
            '质量太差了，完全不值这个价格',
            '产品描述与实际不符',
            '包装简陋，产品一般',
            '非常失望，不会再买了',
            '质量一般，价格偏高',
            '客服态度不好，解决问题慢',
            '产品功能有问题，使用不便',
            '发货慢，包装破损',
            '性价比低，不推荐',
            '产品质量差，退货麻烦',
            '服务差，产品也不行',
            '完全不值这个价钱',
            
            # 中性评论
            '整体还可以，符合预期',
            '一般般，没什么特别的',
            '还行吧，凑合能用',
            '有些功能还需要完善',
            '整体感觉还可以',
            '价格合适，质量还行',
            '基本满足需求',
            '普通的产品，没有惊喜',
            '中规中矩，可以接受',
            '功能一般，价格也一般',
            '还算可以，但不突出',
            '符合描述，没有特别之处',
            '质量尚可，服务一般',
            '可以使用，但有改进空间',
            '总体来说还行'
        ],
        'label': [1]*15 + [0]*15 + [2]*15  # 15个正面，15个负面，15个中性
    }
    return pd.DataFrame(data)

def demo_training():
    """演示训练过程"""
    print("=== 离线演示：中文情感分析 ===")
    print("使用简单分类器模拟LoRA微调效果\n")
    
    # 创建数据
    df = create_demo_data()
    print(f"数据集大小: {len(df)}")
    print(f"标签分布: {df['label'].value_counts().to_dict()}")
    
    # 分割数据
    from sklearn.model_selection import train_test_split
    train_df, test_df = train_test_split(df, test_size=0.3, random_state=42, stratify=df['label'])
    
    print(f"训练集: {len(train_df)}, 测试集: {len(test_df)}")
    
    # 创建和训练模型
    model = SimpleChineseSentimentClassifier()
    model.train(train_df['text'].tolist(), train_df['label'].tolist())
    
    # 评估模型
    print("\n=== 模型评估 ===")
    accuracy, report = model.evaluate(test_df['text'].tolist(), test_df['label'].tolist())
    print(f"准确率: {accuracy:.4f}")
    print(f"详细报告:\n{report}")
    
    # 保存模型
    os.makedirs("./models", exist_ok=True)
    model.save("./models/simple_sentiment_model.pkl")
    
    # 测试预测
    print("\n=== 预测测试 ===")
    test_texts = [
        "这个产品真的很棒，强烈推荐！",
        "质量太差了，完全不值这个价格",
        "还可以吧，没什么特别的"
    ]
    
    for text in test_texts:
        result = model.predict(text)
        print(f"文本: {result['text']}")
        print(f"预测: {result['predicted_class']} (置信度: {result['confidence']:.3f})")
        print(f"概率分布: {result['all_probabilities']}")
        print()
    
    print("🎉 离线演示完成！")
    print("\n这个演示展示了:")
    print("✅ 完整的数据处理流程")
    print("✅ 模型训练和评估")
    print("✅ 预测和结果分析")
    print("✅ 模型保存和加载")
    print("\n实际项目中，这些步骤会用LoRA微调的BERT模型来实现")

if __name__ == "__main__":
    demo_training()
